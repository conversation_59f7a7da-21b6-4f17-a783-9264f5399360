* 工具变量回归分析
* 工具变量：坡度极差
* 自变量：climateRiskPublicView
* 因变量：carbonIntensityRegional

* 数据清理：去掉year变量中的"年"字
* 方法1：如果year是字符串变量
replace year = subinstr(year, "年", "", .)
destring year, replace

* 方法2：如果需要从字符串中提取数字
* generate year_clean = real(regexr(year, "年", ""))
* drop year
* rename year_clean year

* 确保year变量是数值型
confirm numeric variable year

* 使用ivreghdfe进行工具变量回归
ivreghdfe carbonIntensityRegional ///
    (climateRiskPublicView = iv_city_other_mean) ///
    firmSize firmAge leverage ownershipBalance stateOwned ///
    tobinsQ1 returnOnEquity secondaryIndustryRatio gdpPerCapita, ///
    absorb(stockCode year) ///
    vce(cluster CITYCODE)

* 显示回归结果
estimates table, b(%9.4f) se(%9.4f) stats(N r2 F)

* 保存回归结果
estimates store iv_regression

* 输出第一阶段回归结果
estat firststage

* 检验工具变量的有效性
estat endogenous