* 工具变量回归分析
* 工具变量：坡度极差
* 自变量：climateRiskPublicView
* 因变量：carbonIntensityRegional

* 使用ivreghdfe进行工具变量回归
ivreghdfe carbonIntensityRegional ///
    (climateRiskPublicView = 驿站数量) ///
    firmSize firmAge leverage ownershipBalance stateOwned ///
    tobinsQ1 returnOnEquity secondaryIndustryRatio gdpPerCapita, ///
    absorb(stockCode year) ///
    vce(cluster CITYCODE)

* 显示回归结果
estimates table, b(%9.4f) se(%9.4f) stats(N r2 F)

* 保存回归结果
estimates store iv_regression

* 输出第一阶段回归结果
estat firststage

* 检验工具变量的有效性
estat endogenous