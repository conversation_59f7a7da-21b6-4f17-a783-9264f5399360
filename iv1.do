* 工具变量回归分析
* 工具变量：坡度极差
* 自变量：climateRiskPublicView
* 因变量：carbonIntensityRegional

* 使用ivreghdfe进行工具变量回归
ivreghdfe carbonIntensityRegional ///
    (climateRiskPublicView = 坡度极差) ///
    firmSize firmAge leverage ownershipBalance stateOwned ///
    tobinsQ1 returnOnEquity secondaryIndustryRatio gdpPerCapita, ///
    absorb(stockCode year) ///
    vce(cluster CITYCODE)

* ========== 详细诊断信息 ==========

* 1. 第一阶段回归详细结果（检验工具变量相关性）
estat firststage

* 2. 内生性检验（Durbin-Wu-Hausman检验）
estat endogenous

* 3. 弱工具变量检验（已包含在firststage中，但单独显示更清楚）
* F统计量应该大于10，理想情况下大于16.38（Stock-Yogo临界值）

* 4. 保存回归结果以便后续比较
estimates store iv_model

* 5. 显示格式化的回归结果表格
estimates table iv_model, b(%9.4f) se(%9.4f) stats(N r2 F) ///
    title("工具变量回归结果：坡度极差作为工具变量")

* 6. 如果需要与OLS结果比较，可以运行以下命令：
* reghdfe carbonIntensityRegional climateRiskPublicView ///
*     firmSize firmAge leverage ownershipBalance stateOwned ///
*     tobinsQ1 returnOnEquity secondaryIndustryRatio gdpPerCapita, ///
*     absorb(stockCode year) vce(cluster CITYCODE)
* estimates store ols_model
* estimates table ols_model iv_model, b(%9.4f) se(%9.4f) stats(N r2 F)