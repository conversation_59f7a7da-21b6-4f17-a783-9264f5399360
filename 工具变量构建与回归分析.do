/*==============================================================================
项目：中国的公众气候风险表达对上市公司碳排放强度的影响 - 工具变量分析
作者：研究团队
日期：2025年1月
数据：alldata9.dta (2011-2022年面板数据)
说明：构建地理/区域聚合工具变量和行业聚合工具变量，使用ivreghdfe进行回归分析
==============================================================================*/

clear all
set more off
set matsize 10000

// 设置工作路径
cd "/Users/<USER>/Desktop/clitansz"

// 载入数据
use "alldata9.dta", clear

/*==============================================================================
第一部分：数据预处理和变量检查
==============================================================================*/

// 检查核心变量
describe stockCode year climateRiskPublicView carbonIntensityRegional CITYCODE industryCode

// 生成行业代码（统一格式）
gen industry2 = substr(industryCode, 1, 2) if length(industryCode) >= 2
replace industry2 = industryCode if length(industryCode) < 2
destring industry2, replace force

// 检查缺失值情况
misstable summarize climateRiskPublicView carbonIntensityRegional CITYCODE industry2

// 删除关键变量缺失的观测值
drop if missing(climateRiskPublicView) | missing(carbonIntensityRegional) | missing(CITYCODE) | missing(industry2)

/*==============================================================================
第二部分：构建地理/区域聚合工具变量
==============================================================================*/

// 1. 同年度同城市其他企业的公众气候风险表达均值
bysort year CITYCODE: egen temp_city_mean = mean(climateRiskPublicView)
bysort year CITYCODE: egen temp_city_count = count(climateRiskPublicView)
gen iv_city_other_mean = (temp_city_mean * temp_city_count - climateRiskPublicView) / (temp_city_count - 1) if temp_city_count > 1
replace iv_city_other_mean = . if temp_city_count <= 1
drop temp_city_mean temp_city_count

label variable iv_city_other_mean "同年度同城市其他企业公众气候风险表达均值"

// 2. 同年度同省份其他企业的公众气候风险表达均值
bysort year Prvcnm_id: egen temp_prov_mean = mean(climateRiskPublicView)
bysort year Prvcnm_id: egen temp_prov_count = count(climateRiskPublicView)
gen iv_prov_other_mean = (temp_prov_mean * temp_prov_count - climateRiskPublicView) / (temp_prov_count - 1) if temp_prov_count > 1
replace iv_prov_other_mean = . if temp_prov_count <= 1
drop temp_prov_mean temp_prov_count

label variable iv_prov_other_mean "同年度同省份其他企业公众气候风险表达均值"

// 3. 同年度同省份其他城市的公众气候风险表达均值
bysort year Prvcnm_id CITYCODE: egen temp_city_in_prov = mean(climateRiskPublicView)
bysort year Prvcnm_id: egen temp_prov_city_mean = mean(temp_city_in_prov)
bysort year Prvcnm_id: egen temp_prov_city_count = count(temp_city_in_prov)
gen iv_prov_other_city_mean = (temp_prov_city_mean * temp_prov_city_count - temp_city_in_prov) / (temp_prov_city_count - 1) if temp_prov_city_count > 1
replace iv_prov_other_city_mean = . if temp_prov_city_count <= 1
drop temp_city_in_prov temp_prov_city_mean temp_prov_city_count

label variable iv_prov_other_city_mean "同年度同省份其他城市公众气候风险表达均值"

/*==============================================================================
第三部分：构建行业聚合工具变量
==============================================================================*/

// 4. 同年度同行业其他企业的公众气候风险表达均值
bysort year industry2: egen temp_ind_mean = mean(climateRiskPublicView)
bysort year industry2: egen temp_ind_count = count(climateRiskPublicView)
gen iv_ind_other_mean = (temp_ind_mean * temp_ind_count - climateRiskPublicView) / (temp_ind_count - 1) if temp_ind_count > 1
replace iv_ind_other_mean = . if temp_ind_count <= 1
drop temp_ind_mean temp_ind_count

label variable iv_ind_other_mean "同年度同行业其他企业公众气候风险表达均值"

// 5. 同年度同行业同省份其他企业的公众气候风险表达均值
bysort year industry2 Prvcnm_id: egen temp_ind_prov_mean = mean(climateRiskPublicView)
bysort year industry2 Prvcnm_id: egen temp_ind_prov_count = count(climateRiskPublicView)
gen iv_ind_prov_other_mean = (temp_ind_prov_mean * temp_ind_prov_count - climateRiskPublicView) / (temp_ind_prov_count - 1) if temp_ind_prov_count > 1
replace iv_ind_prov_other_mean = . if temp_ind_prov_count <= 1
drop temp_ind_prov_mean temp_ind_prov_count

label variable iv_ind_prov_other_mean "同年度同行业同省份其他企业公众气候风险表达均值"

// 6. 同年度同行业不同地区企业的公众气候风险表达均值
bysort year industry2 CITYCODE: egen temp_ind_city = mean(climateRiskPublicView)
bysort year industry2: egen temp_ind_diff_city_mean = mean(temp_ind_city)
bysort year industry2: egen temp_ind_diff_city_count = count(temp_ind_city)
gen iv_ind_diff_city_mean = (temp_ind_diff_city_mean * temp_ind_diff_city_count - temp_ind_city) / (temp_ind_diff_city_count - 1) if temp_ind_diff_city_count > 1
replace iv_ind_diff_city_mean = . if temp_ind_diff_city_count <= 1
drop temp_ind_city temp_ind_diff_city_mean temp_ind_diff_city_count

label variable iv_ind_diff_city_mean "同年度同行业不同地区企业公众气候风险表达均值"

/*==============================================================================
第四部分：工具变量描述性统计
==============================================================================*/

// 工具变量描述性统计
summarize iv_city_other_mean iv_prov_other_mean iv_prov_other_city_mean ///
         iv_ind_other_mean iv_ind_prov_other_mean iv_ind_diff_city_mean

// 工具变量与核心变量的相关性分析
correlate climateRiskPublicView carbonIntensityRegional ///
          iv_city_other_mean iv_prov_other_mean iv_ind_other_mean

/*==============================================================================
第五部分：定义控制变量
==============================================================================*/

// 企业层面控制变量
global firm_controls "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ1 returnOnEquity"

// 地区层面控制变量  
global region_controls "secondaryIndustryRatio gdpPerCapita"

// 检查控制变量缺失情况
misstable summarize $firm_controls $region_controls

// 对连续变量进行缩尾处理（1%和99%分位数）
foreach var of varlist climateRiskPublicView carbonIntensityRegional $firm_controls $region_controls {
    winsor2 `var', replace cuts(1 99)
}

/*==============================================================================
第六部分：基准OLS回归（用于对比）
==============================================================================*/

// 基准OLS回归
reghdfe carbonIntensityRegional climateRiskPublicView $firm_controls $region_controls, ///
    absorb(stockCode year) vce(cluster CITYCODE)

estimates store ols_baseline

/*==============================================================================
第七部分：工具变量回归分析 - ivreghdfe
==============================================================================*/

// 1. 使用单个工具变量：同城市其他企业均值
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first

estimates store iv_city
estadd scalar fstat = e(widstat)  // 第一阶段F统计量

// 2. 使用单个工具变量：同省份其他企业均值
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_prov_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first

estimates store iv_prov
estadd scalar fstat = e(widstat)

// 3. 使用单个工具变量：同行业其他企业均值
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_ind_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first

estimates store iv_ind
estadd scalar fstat = e(widstat)

// 4. 使用多个工具变量：地理聚合工具变量组合
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_prov_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first

estimates store iv_geo_multi
estadd scalar fstat = e(widstat)
estadd scalar hansen = e(hansenp)  // Hansen过度识别检验p值

// 5. 使用多个工具变量：行业+地理聚合工具变量组合
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first

estimates store iv_geo_ind
estadd scalar fstat = e(widstat)
estadd scalar hansen = e(hansenp)

// 6. 使用全部工具变量
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_prov_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first

estimates store iv_all
estadd scalar fstat = e(widstat)
estadd scalar hansen = e(hansenp)

/*==============================================================================
第八部分：回归结果输出
==============================================================================*/

// 输出主要回归结果对比表
esttab ols_baseline iv_city iv_prov iv_ind iv_geo_multi iv_geo_ind iv_all ///
    using "回归结果_工具变量分析.rtf", ///
    replace b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    keep(climateRiskPublicView) ///
    scalars("fstat 第一阶段F统计量" "hansen Hansen检验p值") ///
    title("公众气候风险表达对企业碳排放强度的影响：工具变量分析") ///
    mtitles("OLS基准" "IV-城市" "IV-省份" "IV-行业" "IV-地理组合" "IV-地理行业" "IV-全部") ///
    note("注：***、**、*分别表示在1%、5%、10%水平上显著。标准误聚类到城市层面。" ///
         "所有回归均控制企业和年份固定效应，以及企业和地区控制变量。")

// 输出详细回归结果（包含控制变量）
esttab iv_geo_multi iv_geo_ind iv_all ///
    using "回归结果_详细_工具变量分析.rtf", ///
    replace b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    scalars("fstat 第一阶段F统计量" "hansen Hansen检验p值" "N 观测值" "r2 R方") ///
    title("公众气候风险表达对企业碳排放强度的影响：详细回归结果") ///
    mtitles("IV-地理组合" "IV-地理行业" "IV-全部工具变量") ///
    note("注：***、**、*分别表示在1%、5%、10%水平上显著。标准误聚类到城市层面。" ///
         "所有回归均控制企业和年份固定效应。")

/*==============================================================================
第九部分：工具变量有效性检验
==============================================================================*/

// 第一阶段回归结果（检验工具变量相关性）
display "=== 第一阶段回归：工具变量相关性检验 ==="

// 手动进行第一阶段回归以获得详细结果
reghdfe climateRiskPublicView iv_city_other_mean iv_prov_other_mean iv_ind_other_mean ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) vce(cluster CITYCODE)

estimates store first_stage
test iv_city_other_mean iv_prov_other_mean iv_ind_other_mean
display "第一阶段F统计量: " r(F)

// 工具变量与因变量的直接相关性检验（排他性约束检验）
display "=== 工具变量排他性约束检验 ==="

reghdfe carbonIntensityRegional iv_city_other_mean iv_prov_other_mean iv_ind_other_mean ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) vce(cluster CITYCODE)

estimates store exclusion_test
test iv_city_other_mean iv_prov_other_mean iv_ind_other_mean
display "工具变量对因变量直接影响的F统计量: " r(F)
display "工具变量对因变量直接影响的p值: " r(p)

/*==============================================================================
第十部分：稳健性检验
==============================================================================*/

// 1. 更换因变量度量方式（如果有其他碳排放强度指标）
if !missing(carbonIntensity) {
    ivreghdfe carbonIntensity (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
        $firm_controls $region_controls, ///
        absorb(stockCode year) cluster(CITYCODE) first
    estimates store robust_dv
}

// 2. 更换控制变量（使用不同的托宾Q）
global firm_controls_robust "firmSize firmAge leverage ownershipBalance stateOwned tobinsQ2 returnOnEquity"

ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls_robust $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store robust_controls

// 3. 更换聚类层面（省份聚类）
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(Prvcnm_id) first
estimates store robust_cluster

// 4. 限制样本：剔除极端值
preserve
foreach var of varlist climateRiskPublicView carbonIntensityRegional {
    egen p1_`var' = pctile(`var'), p(1)
    egen p99_`var' = pctile(`var'), p(99)
    drop if `var' < p1_`var' | `var' > p99_`var'
}

ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store robust_sample
restore

/*==============================================================================
第十一部分：异质性分析
==============================================================================*/

// 1. 按企业所有制性质分组
display "=== 按企业所有制性质分组分析 ==="

// 国有企业
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls if stateOwned == 1, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store hetero_soe

// 非国有企业
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls if stateOwned == 0, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store hetero_nonsoe

// 2. 按企业规模分组
display "=== 按企业规模分组分析 ==="

// 生成企业规模分组变量
egen firmsize_median = median(firmSize)
gen large_firm = (firmSize >= firmsize_median)

// 大企业
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls if large_firm == 1, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store hetero_large

// 小企业
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls if large_firm == 0, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store hetero_small

// 3. 按地区发展水平分组
display "=== 按地区发展水平分组分析 ==="

// 生成地区发展水平分组变量
egen gdp_median = median(gdpPerCapita)
gen developed_region = (gdpPerCapita >= gdp_median)

// 发达地区
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls if developed_region == 1, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store hetero_developed

// 欠发达地区
ivreghdfe carbonIntensityRegional (climateRiskPublicView = iv_city_other_mean iv_ind_other_mean) ///
    $firm_controls $region_controls if developed_region == 0, ///
    absorb(stockCode year) cluster(CITYCODE) first
estimates store hetero_underdeveloped

/*==============================================================================
第十二部分：输出异质性分析结果
==============================================================================*/

// 异质性分析结果表
esttab hetero_soe hetero_nonsoe hetero_large hetero_small hetero_developed hetero_underdeveloped ///
    using "异质性分析结果_工具变量.rtf", ///
    replace b(3) se(3) star(* 0.10 ** 0.05 *** 0.01) ///
    keep(climateRiskPublicView) ///
    scalars("fstat 第一阶段F统计量" "hansen Hansen检验p值" "N 观测值") ///
    title("异质性分析：公众气候风险表达对企业碳排放强度的影响") ///
    mtitles("国有企业" "非国有企业" "大企业" "小企业" "发达地区" "欠发达地区") ///
    note("注：***、**、*分别表示在1%、5%、10%水平上显著。标准误聚类到城市层面。" ///
         "所有回归均控制企业和年份固定效应，以及企业和地区控制变量。")

/*==============================================================================
第十三部分：保存数据和清理
==============================================================================*/

// 保存包含工具变量的数据集
save "alldata9_with_iv.dta", replace

// 输出工具变量构建完成信息
display "=== 工具变量构建和回归分析完成 ==="
display "已构建的工具变量："
display "1. iv_city_other_mean: 同年度同城市其他企业公众气候风险表达均值"
display "2. iv_prov_other_mean: 同年度同省份其他企业公众气候风险表达均值"
display "3. iv_prov_other_city_mean: 同年度同省份其他城市公众气候风险表达均值"
display "4. iv_ind_other_mean: 同年度同行业其他企业公众气候风险表达均值"
display "5. iv_ind_prov_other_mean: 同年度同行业同省份其他企业公众气候风险表达均值"
display "6. iv_ind_diff_city_mean: 同年度同行业不同地区企业公众气候风险表达均值"
display ""
display "回归结果已保存到以下文件："
display "- 回归结果_工具变量分析.rtf"
display "- 回归结果_详细_工具变量分析.rtf"
display "- 异质性分析结果_工具变量.rtf"
display ""
display "数据已保存到：alldata9_with_iv.dta"
