*更多数据，请关注公众号【马克数据网】
use "~/Desktop/rawdata.dta", clear
gen hzlng = lng[88]
gen hzlat = lat[88]

geodist lat lng hzlat hzlng, gen(distance)

geodist lat lng hzlat hzlng, miles gen(distance_miles)

geodist lat lng hzlat hzlng, sphere gen(distance_sphere)
save "~/Desktop/result.dta", replace

sort distance
gen scale = _n

twoway (line distance scale) ///
	(line distance_sphere scale), ///
	xtitle("") xlabel("") graphregion(color(white))